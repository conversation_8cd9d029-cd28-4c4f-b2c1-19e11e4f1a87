<template>
  <q-toolbar class="virtual-community-toolbar">
    <!-- Left Section: Hamburger Menu and Logo -->
    <div class="toolbar-left">
      <q-btn
        flat
        dense
        round
        icon="menu"
        aria-label="Menu"
        class="hamburger-btn"
        @click="$emit('toggle-left-drawer')"
      />

      <!-- Logo next to hamburger menu -->
      <router-link to="/" class="logo-link">
        <img
          src="/smile-factory-logo.svg"
          alt="Smile Factory Logo"
          class="toolbar-logo"
        />
      </router-link>
    </div>

    <!-- Center Section: Navigation -->
    <div class="toolbar-center">

      <!-- Quick Navigation Dropdown (moved to center) -->
      <div class="quick-nav-container desktop-only pulse-animation">
        <q-select
          v-model="selectedTab"
          :options="tabOptions"
          filled
          dense
          emit-value
          map-options
          @update:model-value="handleTabChange"
          class="quick-nav-select"
          dropdown-icon="expand_more"
        >
          <template v-slot:prepend>
            <q-icon :name="getTabIcon(selectedTab)" class="nav-icon" />
          </template>
          <template v-slot:after>
            <q-badge
              v-if="getTabNotificationCount(selectedTab) > 0"
              color="red"
              floating
              rounded
              :label="getTabNotificationCount(selectedTab)"
            />
          </template>
        </q-select>

        <!-- Notification Badge for Dropdown Visibility -->
        <q-badge
          color="accent"
          text-color="white"
          floating
          rounded
          label="NEW"
          class="dropdown-visibility-badge"
        />
      </div>
    </div>

    <!-- Right Section: Actions -->
    <div class="toolbar-actions">
      <!-- Create Post Button -->
      <!-- Desktop: Button with text -->
      <q-btn
        unelevated
        rounded
        color="primary"
        icon="add"
        label="Create"
        class="create-btn desktop-only"
        @click="$emit('open-create')"
      />

      <!-- Mobile: Icon only -->
      <q-btn
        flat
        dense
        round
        icon="add"
        color="positive"
        class="create-btn mobile-only mobile-create-icon"
        @click="$emit('open-create')"
      />

      <!-- Notifications -->
      <q-btn
        flat
        dense
        round
        class="notification-btn"
        @click="toggleNotifications"
      >
        <q-icon :name="$q.screen.lt.md ? 'group' : 'notifications'" />
        <!-- Desktop badge -->
        <q-badge
          v-if="unreadCount > 0 && !$q.screen.lt.md"
          color="red"
          floating
          rounded
          :label="unreadCount > 99 ? '99+' : unreadCount"
        />
        <!-- Mobile green dot -->
        <q-badge
          v-if="unreadCount > 0 && $q.screen.lt.md"
          color="positive"
          floating
          class="mobile-notification-dot"
        />
      </q-btn>

      <!-- Auth-Aware User Menu -->
      <template v-if="!isAuthenticated">
        <!-- Show auth buttons when not authenticated -->
        <q-btn-group spread rounded class="auth-btn-group">
          <q-btn
            color="primary"
            no-caps
            @click="triggerSignUp"
            style="background-color: #0D8A3E; color: white; border: none;"
            class="signup-btn"
            size="sm"
          >
            <div class="text-caption">
              Sign Up
            </div>
          </q-btn>
          <q-btn
            @click="triggerSignIn"
            no-caps
            style="background-color: #a4ca39; color: white; border: none;"
            class="signin-btn"
            size="sm"
          >
            <div class="text-caption">
              Sign In
            </div>
          </q-btn>
        </q-btn-group>
      </template>
      <template v-else>
        <!-- Show user menu when authenticated -->
        <q-btn
          flat
          dense
          round
          class="user-menu-btn"
        >
          <q-icon name="account_circle" size="32px" />
          <q-menu>
            <q-list style="min-width: 200px">
              <q-item clickable v-close-popup @click="goToDashboard">
                <q-item-section avatar>
                  <q-icon name="dashboard" />
                </q-item-section>
                <q-item-section>Dashboard</q-item-section>
              </q-item>
              <q-item clickable v-close-popup @click="goToProfile">
                <q-item-section avatar>
                  <q-icon name="person" />
                </q-item-section>
                <q-item-section>Profile</q-item-section>
              </q-item>
              <q-separator />
              <q-item clickable v-close-popup @click="logout">
                <q-item-section avatar>
                  <q-icon name="logout" />
                </q-item-section>
                <q-item-section>Logout</q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>
      </template>
    </div>

    <!-- Notifications Panel - Disabled -->
    <!-- <q-menu
      v-model="showNotifications"
      anchor="bottom right"
      self="top right"
      class="notifications-menu"
    >
      <div class="notifications-panel">
        <div class="notifications-header">
          <h6>Notifications</h6>
          <q-btn
            flat
            dense
            round
            icon="close"
            @click="showNotifications = false"
          />
        </div>
        <NotificationList />
      </div>
    </q-menu> -->
  </q-toolbar>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { useNotificationStore } from '../../stores/notifications'
import { triggerSignIn, triggerSignUp } from '../../services/unifiedAuthService'
import NotificationList from '../common/NotificationList.vue'

// Props
const props = defineProps<{
  activeTab?: string
}>()

// Emits
const emit = defineEmits<{
  'toggle-left-drawer': []
  'open-create': []
  'trigger-ai': [query: string]
  'tab-change': [tab: string]
}>()

// Composables
const router = useRouter()
const authStore = useAuthStore()
const notificationStore = useNotificationStore()

// State
const showNotifications = ref(false)
const selectedTab = ref(props.activeTab || 'feed')

// Tab options for quick navigation
const tabOptions = [
  { label: 'Community Feed', value: 'feed', icon: 'dynamic_feed' },
  { label: 'Profiles Directory', value: 'profiles', icon: 'people' },
  { label: 'Events', value: 'events', icon: 'event' },
  { label: 'Blog', value: 'blog', icon: 'article' },
  { label: 'Marketplace', value: 'marketplace', icon: 'storefront' }
]

// Computed
const unreadCount = computed(() => notificationStore.unreadCount)
const isAuthenticated = computed(() => authStore.isAuthenticated)

// Methods

function toggleNotifications() {
  // Notifications panel disabled
  // showNotifications.value = !showNotifications.value
}

function handleTabChange(tab: string) {
  selectedTab.value = tab
  emit('tab-change', tab)
}

// Get icon for each tab
function getTabIcon(tab: string) {
  const iconMap: Record<string, string> = {
    feed: 'dynamic_feed',
    profiles: 'people',
    events: 'event',
    blog: 'article',
    marketplace: 'storefront'
  }
  return iconMap[tab] || 'tab'
}

// Get notification count for each tab (placeholder for future implementation)
function getTabNotificationCount(tab: string) {
  // This can be expanded to show actual notification counts per tab
  // For now, return 0 to avoid showing badges
  return 0
}

// Watch for activeTab prop changes
watch(() => props.activeTab, (newTab) => {
  if (newTab) {
    selectedTab.value = newTab
  }
}, { immediate: true })

function goToDashboard() {
  router.push('/dashboard')
}

function goToProfile() {
  router.push('/dashboard/profile')
}

async function logout() {
  try {
    await authStore.signOut()
    // The signOut method already handles navigation and notifications
  } catch (error: any) {
    console.error('Logout error:', error)
    // The signOut method already handles error notifications
  }
}
</script>

<style scoped>
.virtual-community-toolbar {
  height: 64px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.hamburger-btn {
  color: #0D8A3E;

  &:hover {
    background: rgba(13, 138, 62, 0.1);
  }
}

.toolbar-center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 24px;
  max-width: 900px;
  margin: 0 auto;
}

.logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.toolbar-logo {
  height: 40px;
  width: auto;
  transition: transform 0.2s ease;
  
  &:hover {
    transform: scale(1.05);
  }
}

.quick-nav-container {
  min-width: 160px;
  position: relative;
}

/* Pulse animation for dropdown */
.quick-nav-container.pulse-animation {
  animation: dropdown-pulse 2s infinite;
}

@keyframes dropdown-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(13, 138, 62, 0.4);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0 0 8px rgba(13, 138, 62, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(13, 138, 62, 0);
  }
}

/* Dropdown visibility badge */
.dropdown-visibility-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  z-index: 10;
  font-size: 10px;
  font-weight: 600;
  animation: badge-pulse 2s infinite;
}

@keyframes badge-pulse {
  0% {
    transform: scale(0.95);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.8;
  }
}

.quick-nav-select {
  :deep(.q-field__control) {
    border-radius: 20px;
    background: #0D8A3E;
    color: white;
    min-height: 40px;
    border: none;
  }

  :deep(.q-field__native) {
    padding-left: 12px;
    font-size: 14px;
    font-weight: 600;
    color: white;
  }

  :deep(.q-field__prepend) {
    color: white;
  }

  :deep(.q-field__append) {
    color: white;
  }

  :deep(.q-icon) {
    color: white;
  }
}

.nav-icon {
  color: #0D8A3E;
}



.toolbar-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.create-btn {
  font-weight: 600;
  padding: 8px 16px;

  @media (max-width: 768px) {
    .q-btn__content {
      .q-icon {
        margin-right: 0;
      }

      span:not(.q-icon) {
        display: none;
      }
    }
  }
}

/* Mobile create button - icon only without background */
.mobile-create-icon {
  color: #0D8A3E;
  background: transparent !important;
  border: none !important;

  &:hover {
    background: rgba(13, 138, 62, 0.1) !important;
  }

  &:active {
    background: rgba(13, 138, 62, 0.2) !important;
  }

  .q-icon {
    font-size: 24px;
  }
}

/* Mobile notification dot - green round dot without outline */
.mobile-notification-dot {
  width: 8px !important;
  height: 8px !important;
  min-width: 8px !important;
  min-height: 8px !important;
  border-radius: 50% !important;
  border: none !important;
  outline: none !important;
  background: #0D8A3E !important;

  /* Remove any text/label */
  font-size: 0 !important;
  line-height: 0 !important;

  /* Remove padding */
  padding: 0 !important;
}

.notification-btn,
.user-menu-btn {
  color: #0D8A3E;

  &:hover {
    background: rgba(13, 138, 62, 0.1);
  }
}

/* Remove pulse effects on mobile for notification button */
@media (max-width: 768px) {
  .notification-btn {
    animation: none !important;

    .q-badge {
      animation: none !important;
    }
  }
}

/* Auth button group styles - unified capsule design */
.auth-btn-group {
  overflow: hidden;
  display: flex;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .signup-btn,
  .signin-btn {
    color: white;
    transition: all 0.3s ease;
    white-space: nowrap;
    padding: 8px 16px;
    border-radius: 0;
    flex: 1 1 50%;
    min-width: 0;
    letter-spacing: 0.5px;
    font-weight: 600;

    .text-caption {
      font-size: 12px;
    }
  }

  .signup-btn:hover {
    background-color: #0B7A36 !important;
    opacity: 0.9;
  }

  .signin-btn:hover {
    background-color: #95B835 !important;
    opacity: 0.9;
  }
}



.notifications-menu {
  margin-top: 8px;
}

.notifications-panel {
  width: 350px;
  max-height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  
  h6 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #111827;
  }
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .toolbar-center {
    gap: 16px;
  }
  

}

@media (max-width: 768px) {
  .virtual-community-toolbar {
    padding: 0 12px;
    gap: 12px;
  }
  
  .toolbar-center {
    gap: 12px;
  }
  

}

/* Mobile search button styles */
.mobile-search-btn {
  color: rgba(255, 255, 255, 0.8);
}

.mobile-search-btn:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
}

/* Responsive classes */
.desktop-only {
  display: flex;
}

.mobile-only {
  display: none;
}

@media (max-width: 768px) {
  .desktop-only {
    display: none !important;
  }

  .mobile-only {
    display: flex !important;
  }



  .auth-btn-group {
    border-radius: 18px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    min-width: 140px;
    max-width: 140px;

    .q-btn {
      flex: 1 1 50%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      box-shadow: none;
    }

    .signup-btn, .signin-btn {
      font-size: 0.75rem;
      padding: 4px 8px;
      flex: 1 1 50%;
      width: 50%;
      min-width: 0;
      border-radius: 0;
    }
  }
}

@media (max-width: 480px) {
  .toolbar-logo {
    height: 32px;
  }
  

  
  .toolbar-actions {
    gap: 4px;
  }
}
</style>
